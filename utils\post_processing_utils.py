import json
from typing import Any, Dict, List, Optional, Union
import ftfy
import unicodedata
import re
import logging
import re
from exceptions import CustomError
from fastapi import status


def extract_json_with_regex(
    response: str, context_json: Optional[Dict[str, Any]] = None
) -> Union[Dict[str, Any], str]:
    """
    Extracts JSON with regex.
    Args:
        response (str): The response from the LLM.
        context_json (Dict[str, Any], optional): The context JSON. Default is None.
    Returns:
        Union[Dict[str, Any], str]: The extracted JSON or the response if no JSON is found.
    """
    line_re = re.compile(r"\{[^\}]+\}")
    records = line_re.findall(response)
    try:
        if not records:
            raise CustomError(
                "Failed to create JSON response!",
                status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
        final_dict = convert_text_to_dict(records[0])
        if context_json:
            final_dict["Chunks"] = context_json
        return final_dict
    except Exception as e:
        logging.exception(f"Not able to extract <PERSON><PERSON><PERSON> with regex. Error: {e}")
        raise CustomError(
            f"Failed to create JSON response! Error: {e}",
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


def convert_text_to_dict(text):
    cleaned_text = text.replace("```json\n", "").replace("\n```", "")
    try:
        return json.loads(cleaned_text)
    except:
        return eval(cleaned_text)


async def post_process_data_points(
    answers: Dict[str, Any], data_points: List[str]
) -> Dict[str, Any]:
    """
    Post process data points.
    Args:
        answers (dict): The answers.
        data_points (List[str]): The data points.
    Returns:
        dict: The post processed data points.
    """
    updated_answers = {
        data_point: (
            answers[data_point]
            if data_point in answers
            and answers[data_point] is not None
            and "None" != answers[data_point]
            else ""
        )
        for data_point in data_points
    }
    updated_answers["Chunks"] = answers.get("Chunks")
    return updated_answers


def postprocess_llm_text(text):
    normalized = unicodedata.normalize("NFKC", ftfy.fix_text(text))
    return "".join(c for c in normalized if c.isprintable())


def _extract_json_with_regex(response: str) -> Dict[str, Any]:
    """
    Extracts JSON with regex.
    Args:
        response (str): The response from the LLM.
    Returns:
        Dict[str, Any]: The extracted JSON.
    """
    line_re = re.compile(r"\{[^\}]+\}")
    records = line_re.findall(response)
    try:
        if not records:
            return response
        llm_json_response = convert_text_to_dict(records[0])
        return llm_json_response
    except Exception as e:
        logging.exception(f"Not able to extract JSON with regex. Error: {e}")
        return response


def extract_json_from_text(llm_text_response: str) -> Dict[str, Any]:
    """
    Extracts JSON from text.
    Args:
        llm_text_response (str): The response from the LLM.
    Returns:
        Dict[str, Any]: The extracted JSON.
    """
    try:
        llm_json_response = convert_text_to_dict(llm_text_response)
    except Exception as e:
        logging.exception(
            f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
        )
        llm_json_response = _extract_json_with_regex(llm_text_response)

    if isinstance(llm_json_response, dict):
        return llm_json_response
    return None
