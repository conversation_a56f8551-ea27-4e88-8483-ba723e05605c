from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
from constants import (
    DISABLE_BASIC_AUTH,
    LLM_PLATFORM_SUMMARIZE_VOB,
    LLM_REASONING_ENABLED_SUMMARIZE_VOB,
    DEFAULT_VOB_SUMMARY_SYSTEM_PROMPT,
)
from file_processing.file_contents import get_file_contents
from auth import authenticate_request
from utils.document_utils import SourceDocument
from services.summarization_service import summarize
from utils.enums import SummaryMethod
from utils.enums import (
    CloudPlatform,
    VOBSummarizationPrompt,
    EndpointDescriptions,
)
from utils.cloud_utils import (
    get_endpoint_llm_platform,
    get_endpoint_llm_reasoning_enabled,
)
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

# Get the endpoint model configurations
LLM_PLATFORM = get_endpoint_llm_platform(LLM_PLATFORM_SUMMARIZE_VOB, "/summarize-vob")
LLM_REASONING = get_endpoint_llm_reasoning_enabled(
    LLM_REASONING_ENABLED_SUMMARIZE_VOB, "/summarize-vob"
)

vob_summary_router = APIRouter()


@vob_summary_router.post(
    "/summarize-vob",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.summary,
)
async def vob_summarization(
    request: Request,
    x12: UploadFile = File(
        None,
        description="X12 file to be processed. For X12 files with size less than 5MB but usage of x12_url is recommended.",
    ),
    x12_url: str = Form(
        None,
        description="X12 file url to be fetched and processed. For X12 files with size more than 5MB.",
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform to use, could be 'azure' or 'aws'. Default is {LLM_PLATFORM}.",
    ),
    act_prompt: str = Form(
        DEFAULT_VOB_SUMMARY_SYSTEM_PROMPT,
        description="Optional: Acting Prompt for LLM, like 'Act as a Skilled Nursing healthcare expert....'.",
    ),
    summary_prompts: VOBSummarizationPrompt = Form(
        None,
        description="Optional: Prompts for AI on how and what to focus while summarizing.",
    ),
    llm_reasoning_enabled: str = Form(
        LLM_REASONING,
        enum=["true", "false"],
        description=f"Optional: Enable LLM Reasoning to create chain-of-thoughts before answering, could be 'true' or 'false'. By default {LLM_REASONING}.",
    ),
) -> dict:
    """
    Summarize the PDF document.
    Args:
        request (Request): The request object.
        x12 (UploadFile, optional): The X12 file to be processed.
        x12_url (str, optional): The URL of the X12 file.
        llm_platform (Platform, optional): The LLM platform to use.
        act_prompt (str, optional): The acting prompt.
        summary_prompts (VOBSummarizationPrompt, optional): The prompts for AI on how and what to focus while summarizing.
        llm_reasoning_enabled (str, optional): The enablement of LLM reasoning.
    Returns:
        dict: The summary of the PDF document.
    """
    text_file_content = await get_file_contents("txt", x12_url, x12)
    summary_text = summarize(
        request,
        SourceDocument(document_text=text_file_content.decode("utf-8")),
        act_prompt,
        summary_prompts,
        llm_platform,
        SummaryMethod.Stuff,
        llm_reasoning_enabled,
    )
    response = {"vobSummary": summary_text}
    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response
    )
    return response
