from utils.enums import CloudPlatform, OCRType
from services.cloud_services.aws_cloud import <PERSON><PERSON><PERSON><PERSON><PERSON>
from services.cloud_services.azure_cloud import A<PERSON><PERSON>elper
from typing import Union
from constants import (
    AZURE_OPEN_AI_DEPLOYMENT_NAME,
    AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME,
    AZURE_OPEN_AI_EMBEDDING_DEPLOYMENT_NAME,
    BEDROCK_EMBEDDING_MODEL_ID,
    BEDROCK_LLM_MODEL_ID,
    DEFAULT_OCR_TYPE,
    DEFAULT_LLM_PLATFORM,
    DEFAULT_EMBEDDING_PLATFORM,
    DEFAULT_LLM_REASONING_ENABLED,
)


def get_cloud_object(platform: str) -> Union[AzureHelper, AWSHelper]:
    """
    Get the cloud object based on the platform.
    Args:
        platform (str): The platform.
    Returns:
        Union[AzureHelper, AWSHelper]: The cloud object based on the platform.
    """
    return AzureHelper() if platform == CloudPlatform.azure else AWSHelper()


def get_embedding_deployment_name(platform: str) -> str:
    """
    Get the embedding deployment/model name based on the platform.
    Args:
        platform (str): The platform.
    Returns:
        str: The embedding deployment/model name.
    """
    return (
        AZURE_OPEN_AI_EMBEDDING_DEPLOYMENT_NAME
        if platform == CloudPlatform.azure
        else BEDROCK_EMBEDDING_MODEL_ID
    )


def get_llm_deployment_name(platform: str, llm_reasoning_enabled: str = "false") -> str:
    """
    Get the LLM deployment/model name based on the platform.
    Args:
        platform (str): The platform.
    Returns:
        str: The LLM deployment/model name.
    """
    if platform == CloudPlatform.azure:
        if llm_reasoning_enabled == "true":
            return AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME
        else:
            return AZURE_OPEN_AI_DEPLOYMENT_NAME
    return BEDROCK_LLM_MODEL_ID


def get_endpoint_ocr_platform(ocr_platform: str, endpoint: str) -> str:
    """
    Get the endpoint specific OCR platform to use, or a default if none specified.
    Args:
        ocr_platform (str): The OCR platform.
    Returns:
        str: The OCR platform to use.
    """
    if ocr_platform:
        VALID_OCR_TYPES = {e.value for e in OCRType}
        if ocr_platform not in VALID_OCR_TYPES:
            raise ValueError(
                f"Invalid OCR Platform for {endpoint}: '{ocr_platform}'. Must be one of {', '.join(VALID_OCR_TYPES)}."
            )
        return ocr_platform
    return DEFAULT_OCR_TYPE


def get_endpoint_llm_platform(llm_platform: str, endpoint: str) -> str:
    """
    Get the endpoint specific LLM platform to use, or a default if none specified.
    Args:
        llm_platform (str): The LLM platform.
    Returns:
        str: The LLM platform to use.
    """
    if llm_platform:
        VALID_LLM_PLATFORMS = {e.value for e in CloudPlatform}
        if llm_platform not in VALID_LLM_PLATFORMS:
            raise ValueError(
                f"Invalid LLM Platform for {endpoint}: '{llm_platform}'. Must be one of {', '.join(VALID_LLM_PLATFORMS)}."
            )
        return llm_platform
    return DEFAULT_LLM_PLATFORM


def get_endpoint_embedding_platform(embedding_platform: str, endpoint: str) -> str:
    """
    Get the endpoint specific Embedding platform to use, or a default if none specified.
    Args:
        embedding_platform (str): The Embedding platform.
    Returns:
        str: The Embedding platform to use.
    """
    if embedding_platform:
        VALID_EMBEDDING_PLATFORMS = {e.value for e in CloudPlatform}
        if embedding_platform not in VALID_EMBEDDING_PLATFORMS:
            raise ValueError(
                f"Invalid Embedding Platform for {endpoint}: '{embedding_platform}'. Must be one of {', '.join(VALID_EMBEDDING_PLATFORMS)}."
            )
        return embedding_platform
    return DEFAULT_EMBEDDING_PLATFORM


def get_endpoint_llm_reasoning_enabled(
    llm_reasoning_enabled: str, endpoint: str
) -> str:
    """
    Get the endpoint specific LLM reasoning enabled flag to use, or a default if none specified.
    Args:
        llm_reasoning_enabled (str): The LLM reasoning enabled flag.
        endpoint (str): The endpoint name.
    Returns:
        str: The LLM reasoning enabled flag to use.
    """
    if llm_reasoning_enabled:
        VALID_LLM_REASONING_VALUES = {"true", "false"}
        if llm_reasoning_enabled not in VALID_LLM_REASONING_VALUES:
            raise ValueError(
                f"Invalid LLM Reasoning value for {endpoint}: '{llm_reasoning_enabled}'. Must be one of {', '.join(VALID_LLM_REASONING_VALUES)}."
            )
        return llm_reasoning_enabled
    return DEFAULT_LLM_REASONING_ENABLED
