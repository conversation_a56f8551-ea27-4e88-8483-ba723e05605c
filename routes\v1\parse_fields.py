from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import (
    DISABLE_BASIC_AUTH,
    LLM_PLATFORM_EXTRACT_HTML_FIELDS,
    EMBEDDING_PLATFORM_EXTRACT_HTML_FIELDS,
    RAG_ENABLED,
    LLM_REASONING_ENABLED_EXTRACT_HTML_FIELDS,
)
from services.extract_fields_service import (
    extract_data_points_with_context,
)
from utils.document_utils import SourceDocument
from file_processing.text_extraction import get_html_text
from auth import authenticate_request
from file_processing.file_contents import get_file_contents
from utils.enums import CloudPlatform, ExtractionType, EndpointDescriptions, Questions
from utils.input_utils import validate_user_questions
from utils.cloud_utils import (
    get_endpoint_llm_platform,
    get_endpoint_embedding_platform,
    get_endpoint_llm_reasoning_enabled,
)

# Get the endpoint model configurations
LLM_PLATFORM = get_endpoint_llm_platform(
    LLM_PLATFORM_EXTRACT_HTML_FIELDS, "/extract-html-fields"
)
EMBEDDING_PLATFORM = get_endpoint_embedding_platform(
    EMBEDDING_PLATFORM_EXTRACT_HTML_FIELDS, "/extract-html-fields"
)
LLM_REASONING = get_endpoint_llm_reasoning_enabled(
    LLM_REASONING_ENABLED_EXTRACT_HTML_FIELDS, "/extract-html-fields"
)
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

parse_html_fields_router = APIRouter()


@parse_html_fields_router.post(
    "/parseFields",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.parseHTML,
)
@parse_html_fields_router.post(
    "/extract-html-fields",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.parseHTML,
)
async def parse_html_fields(
    request: Request,
    html: UploadFile = File(
        None,
        description="HTML file to be processed. For html files with size less than 5MB but usage of html_url is recommended.",
    ),
    html_url: str = Form(
        None,
        description="HTML file url to be fetched and processed. For HTML files with size more than 5MB.",
    ),
    queries: str = Form(None, description="Comma separated query fields."),
    data_points: Questions = Form(
        None,
        description="List of data points for the AI to extract.",
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform to use, could be 'azure' or 'aws'. Default is {LLM_PLATFORM}.",
    ),
    embedding_platform: CloudPlatform = Form(
        EMBEDDING_PLATFORM,
        description=f"Optional: Embedding Platform to use, could be 'azure' or 'aws'. Default is {EMBEDDING_PLATFORM}.",
    ),
    extraction_type: ExtractionType = Form(
        ExtractionType.AllField,
        description="Optional: Extraction Type, could be 'af' or 'sf'. By default af.",
    ),
    rag_enabled: str = Form(
        RAG_ENABLED,
        enum=["true", "false"],
        description=f"Optional: Specifies whether to use RAG or skip it, the values could be true or false. Default is {RAG_ENABLED}",
    ),
    llm_reasoning_enabled: str = Form(
        LLM_REASONING,
        enum=["true", "false"],
        description=f"Optional: Enable LLM Reasoning to create chain-of-thoughts before answering, could be 'true' or 'false'. By default {LLM_REASONING}.",
    ),
) -> dict:
    """
    Parse fields from the HTML document.
    Args:
        request (Request): The request object.
        html (UploadFile, optional): The HTML file to be processed.
        html_url (str, optional): The URL of the HTML file.
        queries (str, optional): The comma separated query fields.
        data_points (Questions, optional): The list of data points for the AI to extract.
        llm_platform (CloudPlatform, optional): The LLM platform to use.
        embedding_platform (CloudPlatform, optional): The embedding platform to use.
        extraction_type (ExtractionType, optional): The extraction type.
    Returns:
        dict: The parsed fields.
    """
    html_file_content = await get_file_contents("html", html_url, html)

    logging.info(f"query: {queries}")
    logging.info(f"model_type: {extraction_type}")

    data_points = await validate_user_questions(queries, data_points)

    logging.info("Extracing text..")
    html_text = await get_html_text(html_file_content)
    logging.info("Extracting fields.")

    response = await extract_data_points_with_context(
        request,
        extraction_type,
        data_points,
        SourceDocument(document_text=html_text),
        llm_platform,
        embedding_platform,
        html=True,
        rag_enabled=rag_enabled,
        llm_reasoning_enabled=llm_reasoning_enabled,
    )
    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response
    )
    return response
