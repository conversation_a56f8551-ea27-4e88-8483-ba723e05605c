from routes.v1.index import index_router
from routes.v1.ocr import ocr_router
from routes.v1.frequency import frequency_router
from routes.v1.qa import qa_router
from routes.v1.summary import summary_router
from routes.v1.frequency_and_summary import frequency_and_summary_router
from routes.v1.extract_fields import extract_pdf_fields_router
from routes.v1.parse_fields import parse_html_fields_router
from routes.v1.ocr_and_embeddings import ocr_and_embeddings_router
from routes.v1.vob_summary import vob_summary_router
from routes.v1.embed_query import embed_query_router
from routes.v1.document_categorization import document_categorization_router
from routes.v2.frequency import frequency_router as frequency_v2_router

routers = [
    index_router,
    document_categorization_router,
    extract_pdf_fields_router,
    qa_router,
    summary_router,
    frequency_router,
    frequency_v2_router,
    vob_summary_router,
    frequency_and_summary_router,
    parse_html_fields_router,
    embed_query_router,
    ocr_router,
    ocr_and_embeddings_router,
]
