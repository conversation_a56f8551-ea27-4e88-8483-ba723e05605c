from enum import Enum
from pydantic import BaseModel, model_validator
import json
from constants import (
    DEFAULT_SUMMARY_PROMPT,
    DEFAULT_REFINE_SUMMARY_PROMPT,
    DEFAULT_MAP_SUMMARY_PROMPT,
    DEFAULT_REDUCE_SUMMARY_PROMPT,
    DEFAULT_VOB_SUMMARY_PROMPT,
    DEFAULT_QA_PROMPT,
    DEFAULT_DOCUMENT_CATEGORIZATION_PROMPT,
    FREQUENCY_WORDS_CONTEXT,
    DEFAULT_FREQUENCY_PROMPT,
)
from typing import List


class OCRType(str, Enum):
    Paddle = "paddle"
    Azure = "azure"
    AWS = "aws"


class CloudPlatform(str, Enum):
    azure = "azure"
    aws = "aws"


class ExtractionType(str, Enum):
    AllField = "af"
    SeparateField = "sf"


class SummaryMethod(str, Enum):
    MapReduce = "map_reduce"
    Refine = "refine"
    Stuff = "stuff"


class QAPrompt(BaseModel):
    qa_prompt: str = DEFAULT_QA_PROMPT

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class DocumentCategorizationPrompt(BaseModel):
    document_categorization_prompt: str = DEFAULT_DOCUMENT_CATEGORIZATION_PROMPT

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class FrequencyPrompt(BaseModel):
    frequency_prompt: str = DEFAULT_FREQUENCY_PROMPT

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class Questions(BaseModel):
    questions: List[str] = []

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class SummarizationPrompts(BaseModel):
    summary_prompt: str = DEFAULT_SUMMARY_PROMPT
    refine_prompt: str = DEFAULT_REFINE_SUMMARY_PROMPT
    map_prompt: str = DEFAULT_MAP_SUMMARY_PROMPT
    reduce_prompt: str = DEFAULT_REDUCE_SUMMARY_PROMPT

    def get_refine_summary_prompts(self):
        return self.summary_prompt, self.refine_prompt

    def get_map_reduce_summary_prompts(self):
        return self.map_prompt, self.reduce_prompt

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class VOBSummarizationPrompt(BaseModel):
    vob_summary_prompt: str = DEFAULT_VOB_SUMMARY_PROMPT

    def get_vob_summary_prompt(self):
        return self.vob_summary_prompt

    @model_validator(mode="before")
    @classmethod
    def validate_to_json(cls, value):
        if isinstance(value, str):
            return cls(**json.loads(value))
        return value


class EndpointDescriptions(str, Enum):

    ocr = """## OCR Endpoint

**Description:**
This endpoint extracts text from a PDF document using Optical Character Recognition (OCR) which uses Paddle, Azure or AWS.

**Functionality:**

- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

**Response:**
- Returns the extracted text in a structured JSON format, with page numbers and corresponding text.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file or URL.                       |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while getting PDF from lincdoc.   |
"""

    ocr_and_embeddings = """## OCR and Embeddings Endpoint

**Description:**
This endpoint extracts text from a PDF document using Optical Character Recognition (OCR) which uses Paddle, Azure or AWS and also generates embeddings for the extracted text.

**Functionality:**

- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

**Response:**
- Returns the extracted text in a structured JSON format, with page numbers and corresponding text.
- Returns the embeddings for the extracted text.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file or URL.                       |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while getting PDF from lincdoc.   |
"""

    frequency = """## Frequency Endpoint

**Description:**
This endpoint calculates and return the frequency of keywords with bounding boxes from the given PDF document.

**Functionality:**
- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers an option to send a **ocr_data** (JSON file) which is a pre-processed OCR JSON file (generated from the **/ocr** endpoint) bypasses internal OCR processing.

- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

- Processes the extracted text to identify and count occurrences of keywords provided in a comma-separated string (**keywords** parameter).

**Response:**
- Returns a structured response containing keyword frequencies with pages and bounding boxes.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file/URL, invalid JSON format for **ocr_data**, or unsupported file type for **ocr_data**. |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while reading PDF from lincdoc.   |
"""
    qa = """## QA Endpoint

**Description:**
This endpoint functions as a question-and-answer system for PDF documents. It employs an AI model to answer your questions from the document.

**Functionality:**
- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers an option to send a **ocr_data** (JSON file) which is a pre-processed OCR JSON file (generated from the **/ocr** endpoint) bypasses internal OCR processing.
    
- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

- Takes a comma-separated list of questions as input.

- For each question, the AI model searches the PDF text for the most relevant answer and returns the exact corresponding passage where the answer is found.

**Response:**
- Returns a structured response containing answers to your questions, along with the exact text snippets from the PDF that support the answers.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file/URL, invalid JSON format for **ocr_data**, or unsupported file type for **ocr_data**. |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while reading PDF from lincdoc.   |
"""

    summary = """## Summary Endpoint

**Description:**
This endpoint generates summary of PDF document. It allows 2 different summarization techniques and summarize it accordingly.

**Functionality:**
- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers an option to send a **ocr_data** (JSON file) which is a pre-processed OCR JSON file (generated from the **/ocr** endpoint) bypasses internal OCR processing.

- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

- Provides options for summarization methods and prompts:
    - Summarization Method (method): Choose between **map_reduce** (default) or **refine** methods.
        - MapReduce: Breaks down the document and summarizes sections before combining them for a concise summary.
        - Refine: It break down the document, refine the summary by taking preceding section's summary into consideration.

    - Acting Prompt (**act_prompt**): (Optional) Provide context for the summarization process. For example, "Act as a financial analyst...".
    - Summary Prompts (**summary_prompts**): (Optional) Specify prompts to guide the AI on how and what to focus on while summarizing.

**Response:**
- Returns a generated summary of the PDF document based on the chosen method and prompts.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file/URL, invalid JSON format for **ocr_data**, or unsupported file type for **ocr_data**. |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while reading PDF from lincdoc.   |
"""

    frequency_summary = """## Frequency and Summary Endpoint

**Description:**
This endpoint is a combination of both Frequency and Summary Endpoint. It calculates and return the frequency of keywords with bounding boxes from the given PDF document as well as generates summary of PDF document.

**Functionality:**
- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers an option to send a **ocr_data** (JSON file) which is a pre-processed OCR JSON file (generated from the **/ocr** endpoint) bypasses internal OCR processing.

- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

- Processes the extracted text to identify and count occurrences of keywords provided in a comma-separated string (**keywords** parameter).

- Provides options for summarization methods and prompts:
    - Summarization Method (method): Choose between **map_reduce** (default) or **refine** methods.
        - MapReduce: Breaks down the document and summarizes sections before combining them for a concise summary.
        - Refine: It break down the document, refine the summary by taking preceding section's summary into consideration.

    - Acting Prompt (**act_prompt**): (Optional) Provide context for the summarization process. For example, "Act as a financial analyst...".
    - Summary Prompts (**summary_prompts**): (Optional) Specify prompts to guide the AI on how and what to focus on while summarizing.

**Response:**
- Returns a structured response containing keyword frequencies with pages and bounding boxes as well as generated summary of the PDF document based on the chosen method and prompts.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file/URL, invalid JSON format for **ocr_data**, or unsupported file type for **ocr_data**. |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while reading PDF from lincdoc.   |
"""

    parseHTML = """## Parse Fields Endpoint

**Description:**
This endpoint parses specified data fields from an HTML document.

**Functionality:**
- Accepts a HTML document in two ways:
    - Upload a HTML file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible HTML (for larger files).

- Takes a comma-separated list of field names (**queries**) to be extracted from the HTML document.

- Offers an option for extraction type (**extraction_type**)
    - All Fields (default): Attempts to extract all data fields at once using the AI model.
    - Specific Fields: Process one data point at a time using AI, combine all and return the result.

**Response:**
- Returns a JSON object containing the data points based on provided comma separated queries.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing HTML file/URL.                         |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading HTML.                 |
| -           | Could be various errors while reading HTML from lincdoc.  |
"""
    extractField = """## Extract Fields Endpoint

**Description:**
This endpoint extracts specific data fields from a PDF document. It utilizes an AI model to identify and extract the requested data points.

**Functionality:**
- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers an option to send a **ocr_data** (JSON file) which is a pre-processed OCR JSON file (generated from the **/ocr** endpoint) bypasses internal OCR processing.

- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

- Offers an option for extraction type (**extraction_type**)
    - All Fields (default): Attempts to extract all data fields at once using the AI model.
    - Specific Fields: Process one data point at a time using AI, combine all and return the result.

- Takes a comma-separated list of field names (**queries**) to be extracted from the PDF document.

**Response:**
- Returns a JSON object containing the data points based on provided comma separated queries.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file/URL, invalid JSON format for **ocr_data**, or unsupported file type for **ocr_data**. |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while reading PDF from lincdoc.   |
"""

    embed_query = """## Embed Query Endpoint

**Description:**
This endpoint converts the user questions into vector embeddings for retrieving the most relevant results from knowledge base.

**Functionality:**    
- Takes a list of questions as input that needs to be embedded.
- For each question, the endpoint returns the embedding separately.

**Response:**
- Returns a structured response containing embeddings against the questions.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 401         | Unauthorized access.                                      |
| 500         | Internal server error.                                    |
"""

    document_categorization = """## Document Categorization Endpoint

**Description:**
This endpoint functions as a document categorization for referral documents. It employs hybrid approach of AI and fuzzy keyword matching to categorize the document.

**Functionality:**
- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers an option to send a **ocr_data** (JSON file) which is a pre-processed OCR JSON file (generated from the **/ocr** endpoint) bypasses internal OCR processing.
    
- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

- For each category prompt, the AI model searches the PDF text for the most relevant chunk and use that to categorize the document.

**Response:**
- Returns a structured response containing existing and non-existing document categories, along with the page numbers and confidence scores.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file/URL, invalid JSON format for **ocr_data**, or unsupported file type for **ocr_data**. |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while reading PDF from lincdoc.   |
"""

    frequency_v2 = f"""## Frequency V2 Endpoint

**Description:**
This endpoint calculates and returns the frequency of keywords with context analysis to filter out false positives. It uses LLM to analyze the context around each keyword occurrence to determine if it's a genuine mention or a false positive.

**Functionality:**
- Accepts a PDF document in two ways:
    - Upload a PDF file (recommended for files under 6MB).
    - Provide a URL to a publicly accessible PDF (for larger files).

- Offers an option to send a **ocr_data** (JSON file) which is a pre-processed OCR JSON file (generated from the **/ocr** endpoint) bypasses internal OCR processing.

- Offers a choice of OCR engines specified through **ocr_type** parameter:
    - Azure OCR (default).
    - AWS OCR (optional, specified through **ocr_type** parameter).
    - PaddleOCR (optional, specified through **ocr_type** parameter).

- Processes the extracted text to identify and count occurrences of keywords provided in a comma-separated string (**keywords** parameter).

- Analyzes the context around each keyword occurrence ({FREQUENCY_WORDS_CONTEXT} words before and after) to determine if it's a positive mention or a false positive.
    - Example: "Patient has a history of smoking" would count as a positive mention of "smoking".
    - Example: "Patient denies smoking" would NOT count as a positive mention of "smoking".

- Offers a choice of LLM platforms for context analysis through **llm_platform** parameter:
    - Azure (default).
    - AWS (optional).

**Response:**
- Returns a structured response containing keyword frequencies with pages and bounding boxes.
- Includes both total count of occurrences and count of positive mentions after context analysis.
- Provides context information for each occurrence to help understand why it was classified as positive or negative.

**Error Responses:**

| Status Code | Description                                               |
|-------------|-----------------------------------------------------------|
| 400         | Invalid or missing PDF file/URL, invalid JSON format for **ocr_data**, or unsupported file type for **ocr_data**. |
| 401         | Unauthorized access.                                      |
| 500         | Internal server error while reading PDF.                  |
| -           | Could be various errors while reading PDF from lincdoc.   |
"""
