from typing import Optional, List, Dict, Any
from fastapi import Request
from services.endpoint_handlers.rag_service import RetrievalAugmentedGeneration
from utils.document_utils import SourceDocument
from constants import RAG_ENABLED, DEFAULT_LLM_REASONING_ENABLED
from utils.post_processing_utils import post_process_data_points


async def extract_data_points_with_context(
    request: Request,
    extraction_method: str,
    data_points: List[str],
    source_document: SourceDocument,
    llm_platform: str,
    embedding_platform: str,
    html: Optional[bool] = None,
    rag_enabled: str = RAG_ENABLED,
    llm_reasoning_enabled: str = DEFAULT_LLM_REASONING_ENABLED,
) -> Dict[str, Any]:
    """
    Extracts data points with context.
    Args:
        request (Request): The request object.
        extraction_method (str): The extraction method. "af" for all fields and "sf" for separate fields.
        data_points (List[str]): The data points.
        document_text (SourceDocument): The document text or json.
        llm_platform (str): The LLM platform.
        embedding_platform (str): The embedding platform.
        html (bool, optional): Whether the document is HTML. Default is None.
        rag_enabled (str, optional): Whether to use RAG or skip it.
        llm_reasoning_enabled (str, optional): Whether to enable LLM reasoning. Default is LLM_REASONING_ENABLED.
    Returns:
        dict: The data points with context.
    """
    rag_service = RetrievalAugmentedGeneration(
        request,
        llm_platform,
        embedding_platform,
        source_document,
        html,
        rag_enabled,
        llm_reasoning_enabled,
    )

    stripped_data_points = [data_point.strip() for data_point in data_points]
    answers = rag_service.extract_data_points(
        stripped_data_points, extraction_method, rag_enabled
    )
    updated_answers = await post_process_data_points(answers, data_points)
    return updated_answers
