from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import (
    DISABLE_BASIC_AUTH,
    LLM_PLATFORM_QA,
    OCR_PLATFORM_QA,
    EMBEDDING_PLATFORM_QA,
    LLM_REASONING_ENABLED_QA,
)
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from services.qa_service import get_answer_with_context
from utils.document_utils import SourceDocument
from utils.enums import (
    OCRType,
    CloudPlatform,
    QAPrompt,
    EndpointDescriptions,
    Questions,
)
from constants import RAG_SIMPLE_PROMPT
from utils.input_utils import validate_user_questions
from utils.cloud_utils import (
    get_endpoint_ocr_platform,
    get_endpoint_llm_platform,
    get_endpoint_embedding_platform,
    get_endpoint_llm_reasoning_enabled,
)
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(OCR_PLATFORM_QA, "/qa")
LLM_PLATFORM = get_endpoint_llm_platform(LLM_PLATFORM_QA, "/qa")
EMBEDDING_PLATFORM = get_endpoint_embedding_platform(EMBEDDING_PLATFORM_QA, "/qa")
LLM_REASONING = get_endpoint_llm_reasoning_enabled(LLM_REASONING_ENABLED_QA, "/qa")

qa_router = APIRouter()


@qa_router.post(
    "/qa",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.qa,
)
async def qa(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    questions: str = Form(
        None,
        description="Comma separated list of questions. Will be deprecated in future versions.",
    ),
    qa_questions: Questions = Form(
        None,
        description="List of questions for the AI to answer.",
    ),
    ocr_data: UploadFile = File(
        None,
        description="Optional: JSON file containing OCR text (Processed by /ocr endpoint). For JSON files with size less than 5MB but usage of ocr_data_url is recommended.",
    ),
    ocr_data_url: str = Form(
        None,
        description="Optional: OCR JSON file url to be fetched and processed. For ocr_data files with size more than 5MB.",
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform to use, could be 'azure' or 'aws'. Default is {LLM_PLATFORM}.",
    ),
    embedding_platform: CloudPlatform = Form(
        EMBEDDING_PLATFORM,
        description=f"Optional: Embedding Platform to use, could be 'azure' or 'aws'. Default is {EMBEDDING_PLATFORM}.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    qa_prompt: QAPrompt = Form(
        None,
        description="Optional: Prompt for AI for Questions-Answers.",
    ),
    objective_questions: bool = Form(
        True,
        description="Optional: Specifies whether to use the /qa endpoint for decision criteria or to provide a general RAG response. If set to False, qa_prompt input from the user will be ignored. Default is True.",
    ),
    llm_reasoning_enabled: str = Form(
        LLM_REASONING,
        enum=["true", "false"],
        description=f"Optional: Enable LLM Reasoning to create chain-of-thoughts before answering, could be 'true' or 'false'. By default {LLM_REASONING}.",
    ),
) -> dict:
    """
    QA route to get answers to the questions from the PDF document.
    Args:
        request (Request): The request object.
        pdf (UploadFile, optional): The PDF file to be processed.
        pdf_url (str, optional): The URL of the PDF file.
        questions (str, optional): The comma separated list of questions.
        qa_questions (Questions, optional): The list of questions for the AI to answer.
        ocr_data (UploadFile, optional): The JSON file containing OCR text.
        ocr_data_url (str, optional): The URL of the JSON file.
        llm_platform (Platform, optional): The LLM platform to use.
        embedding_platform (Platform, optional): The embedding platform to use.
        ocr_type (OCRType, optional): The OCR type.
        qa_prompt (QAPrompt, optional): The prompt for AI for Questions-Answers.
        objective_questions (bool, optional): Use /qa for decision criteria or provide a general RAG response. Default is True.
    Returns:
        dict: The answers to the questions.
    """
    logging.info(f"ocr_type: {ocr_type}")

    # Use the generic RAG prompt if the QA call is not true
    if not objective_questions:
        qa_prompt = RAG_SIMPLE_PROMPT
    elif qa_prompt:
        qa_prompt = qa_prompt.qa_prompt
    else:
        qa_prompt = QAPrompt().qa_prompt

    questions = await validate_user_questions(questions, qa_questions)

    document_json = await get_ocr_text_json(
        ocr_data, ocr_data_url, pdf_url, pdf, ocr_type, request
    )
    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})

    logging.info("Calling the AI Model.")

    response = await get_answer_with_context(
        request,
        questions,
        SourceDocument(document_json=document_json),
        qa_prompt,
        llm_platform,
        embedding_platform,
        rag_enabled="true",
        llm_reasoning_enabled=llm_reasoning_enabled,
    )

    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )

    return response
