from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import DISABLE_BASIC_AUTH, OCR_PLATFORM_OCR
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from utils.enums import OCRType, EndpointDescriptions
from db.db_operations import save_endpoint_summary
from utils.page_range_utils import get_list_of_pages
from utils.cloud_utils import get_endpoint_ocr_platform

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(OCR_PLATFORM_OCR, "/ocr")

ocr_router = APIRouter()


@ocr_router.post(
    "/ocr",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.ocr,
)
async def ocr(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    page_range: str = Form(
        None,
        description="Optional: Page range for PDF pages to use. Example: 1,3,5-10 for pages [1,3,5,6,7,8,9,10]. Default is None.",
    ),
) -> dict:
    """
    OCR route to extract text from a PDF file.
    Args:
        request (Request): The request object.
        pdf (UploadFile): The PDF file to be processed.
        pdf_url (str): The URL of the PDF file to be processed.
        ocr_type (OCRType): The type of OCR to be used.
        page_range (str): Page range for PDF pages to use.
    Returns:
        dict: The extracted text from the PDF file.
    """
    await save_endpoint_summary(request.state.db, request.state.endpoint_summary)

    logging.info(f"ocr_type: {ocr_type}")
    pages_list = await get_list_of_pages(page_range)
    logging.info(f"Pages to use: {pages_list}")
    document_json = await get_ocr_text_json(
        None, None, pdf_url, pdf, ocr_type, request, pages_list
    )
    document_json["usageCost"]["llmCost"] = 0
    document_json["usageCost"]["embeddingCost"] = 0
    return document_json
