from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import DISABLE_BASIC_AUTH, OCR_PLATFORM_FREQUENCY
from services.frequency_service import get_keyword_count
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from utils.enums import OCRType, EndpointDescriptions
from utils.cloud_utils import get_endpoint_ocr_platform
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(OCR_PLATFORM_FREQUENCY, "/frequency")

frequency_router = APIRouter()


@frequency_router.post(
    "/frequency",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.frequency,
)
async def frequency(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    keywords: str = Form(..., description="Comma separated list of keywords."),
    ocr_data: UploadFile = File(
        None,
        description="Optional: JSON file containing OCR text (Processed by /ocr endpoint). For JSON files with size less than 5MB but usage of ocr_data_url is recommended.",
    ),
    ocr_data_url: str = Form(
        None,
        description="Optional: OCR JSON file url to be fetched and processed. For ocr_data files with size more than 5MB.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
) -> dict:
    """
    Calculate the frequency of the keywords in the PDF document.
    Args:
        request (Request): The request object.
        pdf (UploadFile, optional): The PDF file to be processed.
        pdf_url (str, optional): The URL of the PDF file.
        ocr_data (UploadFile, optional): The JSON file containing OCR text.
        ocr_data_url (str, optional): The URL of the JSON file.
        ocr_type (OCRType, optional): The OCR type.
    Returns:
        dict: The frequency of the keywords in the PDF document.
    """
    logging.info(f"ocr_type: {ocr_type}")
    document_json = await get_ocr_text_json(
        ocr_data, ocr_data_url, pdf_url, pdf, ocr_type, request
    )
    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})
    logging.info("Calculating Frequencies.")
    response = get_keyword_count(document_json, keywords)
    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )
    return response
