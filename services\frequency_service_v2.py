from rapidfuzz import fuzz
import logging
from typing import Dict, List, Any, Union
import asyncio
from utils import (
    get_cloud_object,
    get_llm_deployment_name,
)
from services.frequency_service import (
    check_if_box_overlap,
    split_box_text_into_words,
)
from constants import (
    FREQUENCY_SYSTEM_PROMPT,
    FREQUENCY_WORDS_CONTEXT,
    DEFAULT_FREQUENCY_PROMPT,
    LLM_WORKERS,
    FREQUENCY_FUZZY_MATCHING_THRESHOLD,
)
from services.cloud_services.aws_cloud import AWSHelper
from services.cloud_services.azure_cloud import AzureHelper
from fastapi import Request
from models import update_endpoint_summary


async def get_keyword_count_v2(
    request: Request,
    document_json: Dict[str, Any],
    keywords: str,
    frequency_prompt: str = DEFAULT_FREQUENCY_PROMPT,
    llm_platform: str = "azure",
    llm_reasoning_enabled: str = "false",
) -> Dict[str, Any]:
    """
    Get the keyword count with context analysis to filter out false positives.
    Args:
        request (Request): The request object.
        document_json (Dict[str, Any]): The document text JSON.
        keywords_str (str): The comma-separated keywords.
        frequency_prompt (str): The prompt for AI on how to analyze the context for each keyword.
        llm_platform (str): The LLM platform to use for context analysis.
        llm_reasoning_enabled (str): Whether to enable LLM reasoning.
    Returns:
        Dict[str, Any]: The keyword count with context analysis.
    """
    keywords = keywords.split(",")
    logging.info(f"keywords: {keywords}")
    # Remove :,- from the keywords
    keywords = [
        keyword.strip().lower().translate(str.maketrans("", "", ":,-"))
        for keyword in keywords
    ]
    document_json = split_box_text_into_words(document_json)
    potential_matches = await count_keyword_occurrences_with_context(
        document_json, keywords
    )
    analyzed_matches = await analyze_keyword_contexts(
        request,
        potential_matches,
        frequency_prompt,
        llm_platform,
        llm_reasoning_enabled,
    )
    freqs = {}
    freqs["keywords"] = analyzed_matches
    return freqs


async def count_keyword_occurrences_with_context(
    document_json: Dict[str, Any],
    keywords: List[str],
    matching_threshold: int = FREQUENCY_FUZZY_MATCHING_THRESHOLD,
) -> Dict[str, Any]:
    """
    Count the keyword occurrences and collect context for each occurrence.
    Args:
        document_json (Dict[str, Any]): The document text JSON.
        keywords (List[str]): The keywords.
        matching_threshold (int): The matching threshold. Default is 88.
    Returns:
        Dict[str, Any]: The keyword count with context information.
    """
    keywords_freq = {}

    async def process_page(page_num, page_data_dict):
        if "usageCost" in page_num:
            return []

        data = page_data_dict[page_num]["info"]
        page_data = {
            "image_size": page_data_dict[page_num]["image_size"],
            "angle": page_data_dict[page_num]["angle"],
            "extraction": page_data_dict[page_num]["extraction"],
        }

        words = []
        for i, word_info in enumerate(data):
            words.append(
                {
                    "word": word_info[1].lower(),
                    "page_num": page_num,
                    "position": i,
                    "box": word_info[0],
                    "page_data": page_data,
                }
            )
        return words

    page_results = await asyncio.gather(
        *[process_page(page_num, document_json) for page_num in document_json]
    )
    all_words = [word for page_words in page_results for word in page_words]

    async def process_keyword(keyword):
        keyword_len = len(keyword.split())

        if keyword not in keywords_freq:
            keywords_freq[keyword] = {
                "Counts": 0,
                "Pages": {},
                "Contexts": [],
            }

        for i in range(len(all_words) - keyword_len + 1):
            if not all(
                all_words[i]["page_num"] == all_words[i + j]["page_num"]
                for j in range(keyword_len)
            ):
                continue

            candidate = " ".join(all_words[i + j]["word"] for j in range(keyword_len))
            candidate = candidate.translate(str.maketrans("", "", ":,-"))

            similarity_score = fuzz.ratio(keyword, candidate.strip())
            if similarity_score > matching_threshold:
                page_num = all_words[i]["page_num"]

                context_start = max(0, i - FREQUENCY_WORDS_CONTEXT)
                context_end = min(
                    len(all_words), i + keyword_len + FREQUENCY_WORDS_CONTEXT
                )

                context_words = []
                for j in range(context_start, context_end):
                    if j < len(all_words) and all_words[j]["page_num"] == page_num:
                        context_words.append(all_words[j]["word"])

                context_text = " ".join(context_words)
                keyword_position = " ".join(
                    context_words[i - context_start : i - context_start + keyword_len]
                )

                if page_num not in keywords_freq[keyword]["Pages"]:
                    keywords_freq[keyword]["Pages"][page_num] = {
                        "Counts": 1,
                        "Boxes": [all_words[i]["box"]],
                        "page_data": all_words[i]["page_data"],
                    }
                else:
                    result = check_if_box_overlap(
                        keywords_freq[keyword]["Pages"][page_num]["Boxes"],
                        all_words[i]["box"],
                    )
                    if not result[0]:  # If no overlap
                        keywords_freq[keyword]["Pages"][page_num]["Counts"] += 1
                        keywords_freq[keyword]["Pages"][page_num]["Boxes"].append(
                            all_words[i]["box"]
                        )
                keywords_freq[keyword]["Contexts"].append(
                    {
                        "text": context_text,
                        "keyword": keyword_position,
                        "page": page_num,
                        "box": all_words[i]["box"],
                    }
                )
                keywords_freq[keyword]["Counts"] += 1

    await asyncio.gather(*[process_keyword(keyword) for keyword in keywords])

    not_in_dict = [item for item in keywords if item not in keywords_freq]
    for kw in not_in_dict:
        keywords_freq[kw] = {
            "Counts": 0,
            "Pages": None,
            "Contexts": [],
        }

    return keywords_freq


async def analyze_keyword_contexts(
    request: Request,
    keywords_freq: Dict[str, Any],
    frequency_prompt: str = DEFAULT_FREQUENCY_PROMPT,
    llm_platform: str = "azure",
    llm_reasoning_enabled: str = "false",
) -> Dict[str, Any]:
    """
    Analyze the context of each keyword occurrence to determine if it's a positive or negative mention.
    Args:
        request (Request): The request object.
        keywords_freq (Dict[str, Any]): The keyword frequency data with contexts.
        frequency_prompt (str): The prompt for AI on how to analyze the context for each keyword.
        llm_platform (str): The LLM platform to use.
        llm_reasoning_enabled (str): Whether to enable LLM reasoning.
    Returns:
        Dict[str, Any]: The updated keyword frequency data with positive/negative analysis.
    """
    llm_object = get_cloud_object(llm_platform)
    request.state.endpoint_summary.set_llm_platform(llm_object.cloud_name)
    llm_deployment_name = get_llm_deployment_name(llm_platform, llm_reasoning_enabled)
    request.state.endpoint_summary.set_llm_model(llm_deployment_name)

    final_keywords_freq = {}

    semaphore = asyncio.Semaphore(LLM_WORKERS)

    async def process_keyword_analysis(keyword, data):
        if data["Counts"] == 0 or not data["Contexts"]:
            return keyword, {"Counts": 0, "Pages": {}}

        async with semaphore:
            contexts = [ctx["text"] for ctx in data["Contexts"]]

            results = await analyze_contexts_with_llm(
                request,
                keyword,
                contexts,
                frequency_prompt,
                llm_object,
                llm_reasoning_enabled,
            )
            logging.info(
                f"""Context result for '{keyword}': {results["affirmation_status"]}"""
            )

            if results["affirmation_status"].lower() != "affirmed":
                return keyword, {
                    "Counts": 0,
                    "Pages": {},
                    "reasoning": results["reasoning"],
                }
            else:
                return keyword, {
                    "Counts": data["Counts"],
                    "Pages": data["Pages"],
                    "reasoning": results["reasoning"],
                }

    results = await asyncio.gather(
        *[
            process_keyword_analysis(keyword, data)
            for keyword, data in keywords_freq.items()
        ]
    )
    for keyword, result in results:
        final_keywords_freq[keyword] = result

    return final_keywords_freq


async def analyze_contexts_with_llm(
    request: Request,
    keyword: str,
    contexts: List[str],
    frequency_prompt: str,
    llm_object: Union[AzureHelper, AWSHelper],
    llm_reasoning_enabled: str,
) -> Dict[str, Any]:
    """
    Use LLM to analyze if keyword mentions in contexts are positive or negative.
    Args:
        request (Request): The request object.
        keyword (str): The keyword being analyzed.
        contexts (List[str]): List of context strings containing the keyword.
        llm_object (Union[AzureHelper, AWSHelper]): The LLM service object (AWS or Azure).
        llm_reasoning_enabled (str): Whether to enable LLM reasoning.
    Returns:
        Dict: Results of the analysis including positive count and list of positive flags.
    """
    prompt = frequency_prompt.format(keyword=keyword, context=" ".join(contexts))
    response = llm_object.get_llm_response_with_metadata(
        prompt,
        "json_object",
        FREQUENCY_SYSTEM_PROMPT,
        llm_reasoning_enabled,
    )
    request.state.token_tracker.update(
        {
            "total_tokens": response.get("total_tokens", 0),
            "prompt_tokens": response.get("prompt_tokens", 0),
            "completion_tokens": response.get("completion_tokens", 0),
            "total_cost": response.get("total_cost", 0),
        }
    )
    usage = request.state.token_tracker.get_usage()
    update_endpoint_summary(
        request,
        total_word_count=0,
        total_tokens=usage["total_tokens"],
        prompt_tokens=usage["prompt_tokens"],
        completion_tokens=usage["completion_tokens"],
        total_embedding_tokens=usage["total_embedding_tokens"],
        total_cost=usage["total_cost"],
    )
    return response["content"]
