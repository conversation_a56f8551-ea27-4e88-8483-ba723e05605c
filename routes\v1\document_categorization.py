from fastapi import Depends, APIRouter, Request, UploadFile, Form, File, Body
from constants import (
    DISABLE_BASIC_AUTH,
    LLM_PLATFORM_DOCUMENT_CATEGORIZATION,
    OCR_PLATFORM_DOCUMENT_CATEGORIZATION,
    EMBEDDING_PLATFORM_DOCUMENT_CATEGORIZATION,
)
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from services.document_categorization_service import classify_document_into_categories
from utils import (
    SourceDocument,
    OCRType,
    CloudPlatform,
    DocumentCategorizationPrompt,
    EndpointDescriptions,
    save_endpoint_summary_and_add_cost_in_the_response,
    get_endpoint_ocr_platform,
    get_endpoint_llm_platform,
    get_endpoint_embedding_platform,
)
from typing import Dict, Any
from models.document_categorization import DocumentCategoriesConfig

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(
    OCR_PLATFORM_DOCUMENT_CATEGORIZATION, "/document-categorization"
)
LLM_PLATFORM = get_endpoint_llm_platform(
    LLM_PLATFORM_DOCUMENT_CATEGORIZATION, "/document-categorization"
)
EMBEDDING_PLATFORM = get_endpoint_embedding_platform(
    EMBEDDING_PLATFORM_DOCUMENT_CATEGORIZATION, "/document-categorization"
)

document_categorization_router = APIRouter()


@document_categorization_router.post(
    "/document-categorization",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.document_categorization,
)
async def document_categorization(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    ocr_data: UploadFile = File(
        None,
        description="Optional: JSON file containing OCR text (Processed by /ocr endpoint). For JSON files with size less than 5MB but usage of ocr_data_url is recommended.",
    ),
    ocr_data_url: str = Form(
        None,
        description="Optional: OCR JSON file url to be fetched and processed. For ocr_data files with size more than 5MB.",
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform to use, could be 'azure' or 'aws'. Default is {LLM_PLATFORM}.",
    ),
    embedding_platform: CloudPlatform = Form(
        EMBEDDING_PLATFORM,
        description=f"Optional: Embedding Platform to use, could be 'azure' or 'aws'. Default is {EMBEDDING_PLATFORM}.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    prompt: DocumentCategorizationPrompt = Form(
        None,
        description="Optional: Prompt for AI for Document Categorization.",
    ),
    categories: DocumentCategoriesConfig = Body(
        None,
        description="Optional: Custom document categories configuration. If not provided, default categories will be used.",
    ),
) -> Dict[str, Any]:
    """
    Document Categorization route to categorize the document into categories.
    Args:
        request (Request): The request object.
        pdf (UploadFile, optional): The PDF file to be processed.
        pdf_url (str, optional): The URL of the PDF file.
        ocr_data (UploadFile, optional): The JSON file containing OCR text.
        ocr_data_url (str, optional): The URL of the JSON file.
        llm_platform (Platform, optional): The LLM platform to use.
        embedding_platform (Platform, optional): The embedding platform to use.
        ocr_type (OCRType, optional): The OCR type.
        prompt (DocumentCategorizationPrompt, optional): The prompt for AI for Document Categorization.
        categories (DocumentCategoriesConfig, optional): Custom document categories configuration.
    Returns:
        Dict[str, Any]: The categorization results.
    """
    prompt_text = (
        prompt.document_categorization_prompt
        if prompt
        else DocumentCategorizationPrompt().document_categorization_prompt
    )

    document_json = await get_ocr_text_json(
        ocr_data, ocr_data_url, pdf_url, pdf, ocr_type, request
    )
    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})

    response = await classify_document_into_categories(
        request,
        SourceDocument(document_json=document_json),
        prompt_text,
        llm_platform,
        embedding_platform,
        rag_enabled="true",
        custom_categories=categories,
    )

    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )

    return response
