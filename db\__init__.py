from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import TimeoutError
from constants import DATABASE_URL, DISABLE_DATABASE_REPORTING
import logging
import os
from typing import Generator, Union
from models.endpoint_summary import EndpointSummary
from models.ocr_summary import OcrSummary
from time import sleep
from constants import (
    DB_POOL_SIZE,
    DB_MAX_OVERFLOW,
    DB_POOL_TIMEOUT,
    DB_POOL_RECYCLE,
    DB_MAX_RETRIES,
)


# Create engine with connection pooling
engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=DB_POOL_SIZE,
    max_overflow=DB_MAX_OVERFLOW,
    pool_timeout=DB_POOL_TIMEOUT,
    pool_recycle=DB_POOL_RECYCLE,
    pool_pre_ping=True,  # Verifies connections before use
)


# Log pool checkout/checkin events
@event.listens_for(engine, "checkout")
def checkout(dbapi_connection, connection_record, connection_proxy):
    logging.debug("Database connection checked out from pool")


@event.listens_for(engine, "checkin")
def checkin(dbapi_connection, connection_record):
    logging.debug("Database connection returned to pool")


def get_pool_status():
    """Return current status of the connection pool."""
    return {
        "pool_size": DB_POOL_SIZE,
        "max_overflow": DB_MAX_OVERFLOW,
        "connections_in_use": engine.pool.checkedout(),
        "connections_available": engine.pool.checkedin(),
        "total_connections": engine.pool.size(),
    }


SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    Yield a DB session with retry logic when pool is full.
    """
    retries = 0
    while retries <= DB_MAX_RETRIES:
        try:
            db: Session = SessionLocal()
            yield db
            return  # Ensure we exit after yielding
        except TimeoutError as e:
            retries += 1
            if retries <= DB_MAX_RETRIES:
                wait_time = retries * 2
                logging.warning(
                    f"Connection pool full, waiting {wait_time}s before retry {retries}/{DB_MAX_RETRIES}"
                )
                sleep(wait_time)
            else:
                logging.error(
                    f"Failed to get DB connection after {DB_MAX_RETRIES} retries: {e}"
                )
                raise
        except Exception as e:
            logging.error(f"Unexpected DB connection error: {e}")
            raise
        finally:
            if "db" in locals():
                db.close()


async def save_data(db: Session, obj: Union[EndpointSummary, OcrSummary]) -> None:
    """
    Save the object to the database using the provided session.
    """
    if DISABLE_DATABASE_REPORTING != "true":
        try:
            db.add(obj)
            db.commit()
        except Exception as e:
            try:
                db.rollback()
            except Exception:
                pass
            msg = str(e)
            if "remaining connection slots are reserved" in msg:
                logging.critical(
                    "Database connection limit reached! Check pool settings."
                )
            elif "TimeoutError" in msg or "pool timeout" in msg:
                logging.warning("Database connection pool timeout.")
            else:
                logging.exception("Error saving to database.")
    else:
        logging.info("Database save skipped (disabled).")
