from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import (
    DISABLE_BASIC_AUTH,
    OCR_PLATFORM_OCR_AND_EMBEDDING,
    EMBEDDING_PLATFORM_OCR_AND_EMBEDDING,
)
from auth import authenticate_request
from utils.enums import OCRType, CloudPlatform, EndpointDescriptions
from services.ocr_and_embeddings import get_document_embeddings
from utils.cloud_utils import get_endpoint_ocr_platform, get_endpoint_embedding_platform
from file_processing.text_extraction import get_ocr_text_json
from utils.document_utils import SourceDocument
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(
    OCR_PLATFORM_OCR_AND_EMBEDDING, "/ocr-and-embeddings"
)
EMBEDDING_PLATFORM = get_endpoint_embedding_platform(
    EMBEDDING_PLATFORM_OCR_AND_EMBEDDING, "/ocr-and-embeddings"
)

ocr_and_embeddings_router = APIRouter()


@ocr_and_embeddings_router.post(
    "/ocr-and-embeddings",
    tags=["v1"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.ocr_and_embeddings,
)
async def ocr_and_embeddings(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    embedding_platform: CloudPlatform = Form(
        EMBEDDING_PLATFORM,
        description=f"Optional: Embedding Platform to use, could be 'azure' or 'aws'. Default is {EMBEDDING_PLATFORM}.",
    ),
) -> dict:
    """
    OCR route to extract text from a PDF file.
    Args:
        request (Request): The request object.
        pdf (UploadFile): The PDF file to be processed.
        pdf_url (str): The URL of the PDF file to be processed.
        ocr_type (OCRType): The type of OCR to be used.
        embedding_platform (CloudPlatform): The type of embedding platform to be used.
    Returns:
        dict: The extracted text from the PDF file and the embeddings.
    """
    logging.info(f"ocr_type: {ocr_type}")
    document_json = await get_ocr_text_json(None, None, pdf_url, pdf, ocr_type, request)
    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})
    document_embeddings = await get_document_embeddings(
        request,
        SourceDocument(document_json=document_json),
        embedding_platform,
    )
    response = {"ocr_data": document_json, "embeddings": document_embeddings}
    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )
    return response
